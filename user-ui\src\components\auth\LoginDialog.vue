<!--
  登录弹窗组件
  
  @description 用户登录弹窗，支持手机号/邮箱登录
  <AUTHOR>
  @date 2025-07-18 16:00:00 +08:00
  @reference 基于shadcn-vue设计系统
-->

<script setup lang="ts">
import { ref, watch } from 'vue'
import { useRouter } from 'vue-router'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Checkbox } from '@/components/ui/checkbox'
import { Eye, EyeOff, Phone, Mail, Lock, User } from 'lucide-vue-next'
import { useUserStore } from '@/stores/user'
import { useUIStore } from '@/stores/ui'

// Store和Router
const userStore = useUserStore()
const uiStore = useUIStore()
const router = useRouter()

// Props和Emits定义
const props = defineProps({
  open: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:open', 'switch-to-register', 'login-success'])

// 响应式状态
const isOpen = ref(props.open)
const showPassword = ref(false)
const loginType = ref('phone') // 'phone' | 'email'
const loading = ref(false)

// 表单数据
const loginForm = ref({
  phone: '',
  email: '',
  password: '',
  rememberMe: false
})

// 表单验证
const errors = ref({
  phone: '',
  email: '',
  password: ''
})

// 监听props变化
watch(() => props.open, (newVal) => {
  isOpen.value = newVal
})

// 监听内部状态变化
watch(isOpen, (newVal) => {
  emit('update:open', newVal)
})



// 切换密码显示
const togglePasswordVisibility = () => {
  showPassword.value = !showPassword.value
}

// 表单验证
const validateForm = () => {
  errors.value = { phone: '', email: '', password: '' }
  let isValid = true

  if (loginType.value === 'phone') {
    if (!loginForm.value.phone) {
      errors.value.phone = '请输入手机号'
      isValid = false
    }
  } else {
    if (!loginForm.value.email) {
      errors.value.email = '请输入邮箱'
      isValid = false
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(loginForm.value.email)) {
      errors.value.email = '请输入正确的邮箱格式'
      isValid = false
    }
  }

  if (!loginForm.value.password) {
    errors.value.password = '请输入密码'
    isValid = false
  } else if (loginForm.value.password.length < 6) {
    errors.value.password = '密码至少6位'
    isValid = false
  }

  return isValid
}

// 处理登录
const handleLogin = async () => {
  if (!validateForm()) return

  loading.value = true
  try {
    // 准备登录凭据
    const credentials = {
      username: loginType.value === 'phone' ? loginForm.value.phone : loginForm.value.email,
      password: loginForm.value.password,
      rememberMe: loginForm.value.rememberMe
    }

    // 调用用户store的登录方法
    await userStore.login(credentials)

    // 登录成功，关闭弹窗
    isOpen.value = false

    // 跳转到重定向路径
    const redirectPath = uiStore.loginRedirectPath || '/'
    if (redirectPath !== router.currentRoute.value.path) {
      await router.push(redirectPath)
    }

    // 重置表单
    resetForm()

    emit('login-success')
  } catch (error) {
    console.error('登录失败:', error)
    // 错误信息已经在store中通过toast显示了
  } finally {
    loading.value = false
  }
}

// 重置表单
const resetForm = () => {
  loginForm.value = {
    phone: '',
    email: '',
    password: '',
    rememberMe: false
  }
  errors.value = { phone: '', email: '', password: '' }
  showPassword.value = false
}

// 切换到注册
const switchToRegister = () => {
  isOpen.value = false
  emit('switch-to-register')
}

// 关闭弹窗时重置表单
const handleClose = () => {
  resetForm()
}
</script>

<template>
  <Dialog v-model:open="isOpen" @update:open="handleClose">
    <DialogContent class="sm:max-w-md">
      <DialogHeader>
        <DialogTitle class="text-center text-xl font-bold">登录账户</DialogTitle>
        <DialogDescription class="text-center">
          欢迎回来，请登录您的账户
        </DialogDescription>
      </DialogHeader>

      <div class="space-y-6">
        <!-- 登录方式切换 -->
        <Tabs v-model="loginType" class="w-full">
          <TabsList class="grid w-full grid-cols-2">
            <TabsTrigger value="phone" class="flex items-center gap-2">
              <Phone class="h-4 w-4" />
              手机号
            </TabsTrigger>
            <TabsTrigger value="email" class="flex items-center gap-2">
              <Mail class="h-4 w-4" />
              邮箱
            </TabsTrigger>
          </TabsList>

          <!-- 手机号登录 -->
          <TabsContent value="phone" class="space-y-4">
            <div class="space-y-2">
              <Label for="login-phone">手机号</Label>
              <Input
                id="login-phone"
                v-model="loginForm.phone"
                type="tel"
                placeholder="请输入手机号"
                :class="{ 'border-red-500': errors.phone }"
              />
              <p v-if="errors.phone" class="text-sm text-red-500">{{ errors.phone }}</p>
            </div>
          </TabsContent>

          <!-- 邮箱登录 -->
          <TabsContent value="email" class="space-y-4">
            <div class="space-y-2">
              <Label for="login-email">邮箱</Label>
              <Input
                id="login-email"
                v-model="loginForm.email"
                type="email"
                placeholder="请输入邮箱"
                :class="{ 'border-red-500': errors.email }"
              />
              <p v-if="errors.email" class="text-sm text-red-500">{{ errors.email }}</p>
            </div>
          </TabsContent>
        </Tabs>

        <!-- 密码输入 -->
        <div class="space-y-2">
          <Label for="login-password">密码</Label>
          <div class="relative">
            <Input
              id="login-password"
              v-model="loginForm.password"
              :type="showPassword ? 'text' : 'password'"
              placeholder="请输入密码"
              :class="{ 'border-red-500': errors.password }"
              class="pr-10"
            />
            <Button
              type="button"
              variant="ghost"
              size="sm"
              class="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
              @click="togglePasswordVisibility"
            >
              <Eye v-if="showPassword" class="h-4 w-4" />
              <EyeOff v-else class="h-4 w-4" />
            </Button>
          </div>
          <p v-if="errors.password" class="text-sm text-red-500">{{ errors.password }}</p>
        </div>

        <!-- 记住我和忘记密码 -->
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-2">
            <Checkbox id="login-remember" v-model:checked="loginForm.rememberMe" />
            <Label for="login-remember" class="text-sm">记住我</Label>
          </div>
          <Button variant="link" class="px-0 text-sm">
            忘记密码？
          </Button>
        </div>

        <!-- 登录按钮 -->
        <Button 
          @click="handleLogin" 
          class="w-full" 
          :disabled="loading"
        >
          <User v-if="!loading" class="mr-2 h-4 w-4" />
          {{ loading ? '登录中...' : '登录' }}
        </Button>

        <!-- 注册链接 -->
        <div class="text-center text-sm">
          <span class="text-muted-foreground">还没有账户？</span>
          <Button variant="link" class="px-1" @click="switchToRegister">
            立即注册
          </Button>
        </div>
      </div>
    </DialogContent>
  </Dialog>
</template>
